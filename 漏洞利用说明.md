# SPL Loader 签名验证绕过漏洞利用说明

## 🚨 漏洞概述

在splloader分区的签名验证代码中发现严重安全漏洞，攻击者可以通过修改单个字节完全绕过RSA签名验证。

## 漏洞原理

### 漏洞位置
- **函数**: `verify_image_signature` (0x6500BD84)
- **代码行**: 第6909-6914行

### 漏洞代码
```c
v5 = *a3;  // 读取镜像头第一个字节（版本标志）
if ( v5 > 1 )
    return 0;  // 🚨 直接返回0
```

### 上层解释错误
```c
// 在 sub_6500C154 函数中
return (unsigned int)sub_6500C0A8((__int64)v6, a2) == 0;
```
- 返回值0被错误解释为验证成功
- 返回值非0被解释为验证失败

## 攻击方法

### 方法1：版本标志绕过（推荐）

**修改位置**: 镜像头偏移4字节
**原始值**: `01` (sml镜像标识)
**修改值**: `02` (或任何>1的值，如0x03, 0xFF等)

**修改前**:
```
44 48 54 42 01 00 00 00 ...
            ↑
         偏移4: 01
```

**修改后**:
```
44 48 54 42 02 00 00 00 ...
            ↑
         偏移4: 02
```

### 方法2：验证标志绕过

**修改位置**: 镜像头偏移588字节 (0x24C)
**原始值**: `01 00 00 00` (启用验证)
**修改值**: `00 00 00 00` (禁用验证)

## 利用效果

- ✅ **完全绕过RSA签名验证**
- ✅ **可加载任意修改的恶意镜像**
- ✅ **破坏安全启动链完整性**
- ✅ **获得启动阶段完全控制权**

## 技术细节

### 正常验证流程
1. 读取版本标志 (0=uboot, 1=sml)
2. 检查验证标志是否为1
3. 执行RSA-2048/3072签名验证
4. 验证PKCS#1或PSS填充
5. 比较哈希值

### 绕过后流程
1. 读取版本标志 (>1)
2. 直接返回0 ⚠️
3. 上层函数误认为验证成功
4. 跳过所有RSA验证步骤
5. 直接执行恶意镜像

## 影响评估

**严重程度**: 🔴 极高 (CVSS 9.8)
**利用难度**: 🟢 极低 (只需修改1字节)
**影响范围**: 所有使用此splloader的设备

## 修复建议

1. **立即修复**: 修改返回值语义，0表示失败，非0表示成功
2. **代码审计**: 检查所有验证函数的返回值处理
3. **测试验证**: 确保修复后正常镜像仍能通过验证
4. **安全更新**: 推送固件更新到所有受影响设备

## 文件修改记录

- **原始文件**: 新建 文本文档.txt (原始hex数据)
- **修改文件**: 新建 文本文档.txt (已修改版本标志)
- **修改内容**: 偏移4字节从0x01改为0x02

⚠️ **警告**: 此漏洞仅用于安全研究和漏洞修复，请勿用于恶意目的！
